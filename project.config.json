{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "setting": {"packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./miniprogram/"}], "useCompilerPlugins": ["typescript", "sass"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "postcss": false, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": false, "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": true, "es6": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "packOptions": {"ignore": [], "include": []}, "appid": "wx1390f03c83bdebbf"}