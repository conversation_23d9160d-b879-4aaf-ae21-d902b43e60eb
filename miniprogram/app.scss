/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

.weui-navigation-bar__left {
  display: flex;
  align-items: center !important;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

view {
  box-sizing: border-box;
}

// .van-field__body {
//   height: 30px;
// }

// .van-cell__title {
//   height: 30px;
//   display: flex;
//   align-items: center;
// }