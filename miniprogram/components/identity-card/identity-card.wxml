<!--components/identity-card/identity-card.wxml-->
<view class="identity-card">
    <images-url style="position: absolute;top: 0;right: 0;left: 0;bottom: 0;" src="/images/bj3.png" width="690rpx" height="328rpx"></images-url>
    <view class="content">
        <view class="header">
            <view class="title">{{ !info.userId?"游客": ["其他人员","教职工","本科生","研究生"][info.userType] || "-"}}</view>
            <view class="up" bind:tap="onChecked" wx:if="{{!isExpand && info.userId}}">
                {{ !show?"展开":"收起"}}
                <van-icon name="arrow-down" wx:if="{{!show}}" />
                <van-icon name="arrow-up" wx:if="{{show}}" />
            </view>
            <view class="currentIdentity" wx:if="{{currentIdentity}}">
                当前身份
            </view>
        </view>
        <view wx:if="{{(show || isExpand) && info.userId}}">
            <view class="main">
                <view>{{info.deptName || "-"}}</view>
                <view class="tag">
                    企业微信：
                    <view class="tags" wx:if="{{info.openStatus}}">
                        <view class="drop"></view>
                        已开通
                    </view>
                    <view class="tags-no-open" wx:if="{{!info.openStatus}}">
                        <view class="drop"></view>
                        未开通
                    </view>
                </view>
                <view>手机号：{{ info.phoneNo || "-" }}</view>
            </view>
            <view class="bottom">
                <view>学号：{{ info.userId || "-" }}</view>
            </view>
        </view>
    </view>
</view>