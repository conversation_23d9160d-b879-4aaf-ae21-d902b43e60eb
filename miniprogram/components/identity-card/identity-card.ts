// components/identity-card/identity-card.ts
Component({

    /**
     * 组件的属性列表
     */
    properties: {
        isExpand: { // !显示展开收起
            type: Boolean,
            value: false
        },
        currentIdentity: { // 当前身份
            type: Boolean,
            value: false
        },
        info: {
            type: Object,
            value: {}
        }
    },

    /**
     * 组件的初始数据
     */
    data: {
        show: true
    },

    /**
     * 组件的方法列表
     */
    methods: {
        onChecked() {
            const { show } = this.data
            this.setData({
                show: !show
            })
        }
    }
})