/* components/identity-card/identity-card.wxss */
.identity-card {
    margin-bottom: 24rpx;
    padding: 40rpx 40rpx;
    border-radius: 16rpx;
    position: relative;
    height: min-content;
    overflow: hidden;

    .content {
        position: relative;
        color: #ffffff;

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .title {
                font-weight: 600;
                font-size: 36rpx;
                font-family: PingFangSC, PingFang SC;
            }

            .up {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #FFFFFF;
                line-height: 32rpx;
                text-align: left;
                font-style: normal;
            }

            .currentIdentity {
                width: 128rpx;
                height: 48rpx;
                background: #FFFFFF;
                border-radius: 24rpx;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 24rpx;
                color: #1677FF;
                line-height: 32rpx;
                text-align: left;
                font-style: normal;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .main {
            font-size: 24rpx;
            padding: 16rpx 0;

            >view {
                &:not(:nth-of-type(1)) {
                    margin-top: 10rpx;
                }
            }

            .tag {
                display: flex;
                align-items: center;

                .tags {
                    background: #FFFFFF;
                    border-radius: 6rpx;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #0AAF35;
                    line-height: 30rpx;
                    text-align: left;
                    font-style: normal;
                    display: flex;
                    align-items: center;
                    padding: 4rpx 8rpx;

                    .drop {
                        width: 8rpx;
                        height: 8rpx;
                        background: #0AAF35;
                        border-radius: 50%;
                        margin-right: 8rpx;
                    }
                }

                .tags-no-open {
                    background: #FFFFFF;
                    border-radius: 6rpx;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 22rpx;
                    color: #ABABAB;
                    line-height: 30rpx;
                    text-align: left;
                    font-style: normal;
                    display: flex;
                    align-items: center;
                    padding: 4rpx 8rpx;

                    .drop {
                        width: 8rpx;
                        height: 8rpx;
                        background: #ABABAB;
                        border-radius: 50%;
                        margin-right: 8rpx;
                    }
                }
            }


        }

        .bottom {
            font-size: 24rpx;
            border-top: 1px solid rgba(216, 216, 216, 0.4);
            padding-top: 20rpx;
        }
    }
}