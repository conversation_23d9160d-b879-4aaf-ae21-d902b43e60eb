/** 请求封装 */
// const base_url = "http://192.168.3.40:8300"
const base_url = "https://lskj.lskejisoft.com/wx-platform/admin-base/api";

interface requestParams {
  url: string
  data?: any,
  methods: "POST" | "GET"
}

class Request {
  base_url: string
  constructor(base_url: string) {
    this.base_url = base_url
  }
  getHeader() {
    console.log("getHeader")
    return {
      'Authorization': `Bearer ${wx.getStorageSync('tokens')}`
    }
  }
  errDealWith(data: { code: number; msg: string }) {
    switch (data.code) {
      case 401:
        console.error("重新登录")
        wx.removeStorageSync("tokens")
        wx.showToast({
          title: "登录失效",
          icon: 'none',
          // image: '/images/failed.png',
          duration: 2000
        })
        setTimeout(() => {
          wx.reLaunch({
            url: "/pages/index/index"
          })
        }, 1000)
        break
      case 404:
        console.error("未找到资源")
        wx.showToast({
          title: "未找到资源",
          icon: 'none',
          // image: '/images/failed.png',
          duration: 2000
        })
        break
      default: wx.showToast({
        title: data.msg,
        icon: 'none',
        // image: '/images/failed.png',
        duration: 2000
      })
    }
  }
  request(params: requestParams) {
    const { url, data, methods } = params
    console.log(data, "请求参数")
    return new Promise((_resolve, _reject) => {
      console.log(url, "uurrl;ll", this.getHeader())
      wx.request({
        url: this.base_url + url,
        data,
        method: methods,
        header: this.getHeader(),
        success: (response: any) => {
          const { statusCode, data } = response
          if (data.code !== 0 || statusCode !== 200) {
            this.errDealWith(data)
            return _reject(data)
          }
          return _resolve(data)
        },
        fail: (err) => {
          wx.hideLoading()
          _reject(err)
        }
      })
    })
  }
  async get<T>(params: Omit<requestParams, 'methods'>): Promise<{ data: T }> {
    return await this.request({ ...params, methods: 'GET' }) as any
  }
  async post<T>(params: Omit<requestParams, 'methods'>): Promise<{ data: T }> {
    return await this.request({ ...params, methods: 'POST' }) as any
  }
  uploadFile(option: { url: string; filePath: string }) {
    const { url, filePath } = option
    return new Promise((_resolve, _reject) => {
      wx.uploadFile({
        url: this.base_url + url,
        filePath,
        name: "file",
        header: this.getHeader(),
        success(res) {
          // 上传完成需要更新 fileList
          console.log(res, "图片上传回调参数",)
          let { data } = res
          data = JSON.parse(data)
          if (data.code !== 0) {
            _reject(data)
            return
          }
          _resolve(data)
        },
        fail(e) {
          _reject(e)
        }
      });
    })
  }
}
export default new Request(base_url)