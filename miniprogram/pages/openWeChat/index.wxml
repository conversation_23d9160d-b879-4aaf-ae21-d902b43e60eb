<!--index.wxml-->
<navigation-bar title="{{title==='绑定手机号'?'企业微信开通':'换绑手机号'}}" color="black" background="#FFF"></navigation-bar>
<view style="height: 100vh;position: relative;background-color: #F7F8FA;">
  <view style="position: absolute;top: 0;right: 0;left: 0;bottom: 0;">
    <images-url src="/images/bj2.png" width="100vw" height="714rpx"></images-url>
  </view>
  <view class="openWeChat">
    <view class="card">
      <view class="openWeChat-card">
        <images-url style="position: absolute;top: 0;right: 0;left: 0;bottom: 0;" src="/images/bj3.png" width="690rpx" height="328rpx"></images-url>
        <view class="content">
          <view class="header">
            <view class="title">{{currentInfo.userName || "-"}}</view>
          </view>
          <view>
            <view class="main">
              <view>身份：{{ ["其他人员","教职工","本科生","研究生"][currentInfo.userType] || "-"}}<text style="margin-left: 50rpx;">工号：{{ currentInfo.userId || "-" }}</text></view>
              <view>归属：{{currentInfo.deptName || "-"}}</view>
              <view class="tag">
                企业微信开通状态：
                <view class="tags" wx:if="{{currentInfo.openStatus}}">
                  <view class="drop"></view>
                  已开通
                </view>
                <view class="tags-no-open" wx:if="{{!currentInfo.openStatus}}">
                  <view class="drop"></view>
                  未开通
                </view>
              </view>
              <view>手机号：{{currentInfo.phoneNo || "-"}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="form">
      <view class="title">{{title}}</view>
      <van-field wx:if="{{false}}" value="{{ mobile }}" type="number" maxlength="11" label="手机号" placeholder="请输入手机号" data-key="mobile" bind:change="onValueChange" />
      <van-field wx:if="{{false}}" value="{{ code }}" type="number" maxlength="6" label="验证码" placeholder="请输入验证码" data-key="code" bind:change="onValueChange">
        <view slot="button" class="yzm" bind:tap="getCode">
          {{countdown === 0?'获取验证码':'重新发送('+ countdown +')' }}
        </view>
      </van-field>

      <van-field disabled value="{{ mobile }}" type="number" label="" placeholder="请点击获取手机号" data-key="code" bind:change="onValueChange">
        <van-button size="small" slot="button" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" type="info">一键获取</van-button>
      </van-field>
    </view>
    <view class="hint">
      <images-url src="/images/hint.png" width="48rpx" height="48rpx"></images-url>
      <view class="text">
        {{text}}
      </view>
    </view>
    <view class="btn">
      <van-button block type="info" bind:tap="onSubmit">提交</van-button>
    </view>
    <view style="display: flex;justify-content: flex-end;margin-top: 12rpx;">
      <customer-service />
    </view>
  </view>
  <van-dialog id="my-dialog" class='my-dialog' />
  <van-dialog use-slot title="手机号已绑定" show="{{ show }}" show-cancel-button bind:confirm="onConfirm" bind:close="onClose">
    <view class="dialog-confirm">
      <view>
        检测到当前手机号已于{{currentInfo.userName || "-"}}的{{pastUserId}}身份绑定，
        是否将原账号注销，并将手机号绑定到当前身份？该操作会将原账号移出企业微信，之后将新
        账号加入企业微信，
        <span class="red">并会导致原账号的聊天信息、文档等信息被清除</span>
        请注意保存原账号的重要数据后再进行此操作！
      </view>
      <view class="confirm-text">请输入“我已知晓”后进行确定操作</view>
      <van-field value="{{ confirmValue }}" title-width="0" placeholder="请输入" border="{{ false }}" />
    </view>
  </van-dialog>
</view>