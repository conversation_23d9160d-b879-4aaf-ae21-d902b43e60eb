// pages/openWeChat/index.ts
import Dialog from '@/miniprogram_npm/@vant/weapp/dialog/dialog';
import { getCodeApi, perBindingVerifyApi, phoneBindingApi, phoneChangeBindingApi, deregisterBindingApi, getUserPhoneNumberApi } from "@/api/index"


Page({

  /**
   * 页面的初始数据
   */
  data: {
    mobile: "",
    code: "",
    countdown: 0,
    show: false,
    title: "绑定手机号",
    text: "请确保与当前微信号绑定的手机号一致，否则将无法绑定企业微信",
    currentInfo: {},
    type: "1", // 1:换绑 其他：正常
    pastUserId: "",
    confirmValue: "",
    phoneCode: ""
  },
  onShow() {
    this.setData({
      currentInfo: wx.getStorageSync('currentInfo') || {}
    })
  },
  onLoad(options) {
    const { type } = options
    if (type === "1") {
      this.setData({
        title: "换绑手机号",
        text: "请确保是本人操作"
      })
    }
    this.setData({
      type
    })
  },
  async getCode() {
    const { mobile } = this.data
    if (!/^1\d{10}$/.test(mobile)) {
      wx.showToast({
        title: "请输入正确的手机号",
        icon: 'none',
        // image: '/images/failed.png',
        duration: 2000
      })
      return
    }
    const { countdown } = this.data
    if (countdown !== 0) return
    await getCodeApi({
      phoneNo: mobile
    })
    this.timerFn()
  },
  async onSubmit() {
    const { currentInfo, mobile, phoneCode, type } = this.data
    if (!mobile) {
      wx.showToast({
        title: "请先获取手机号",
        icon: 'none',
        // image: '/images/failed.png',
        duration: 2000
      })
      return
    }
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      })
      const { data } = await perBindingVerifyApi({
        name: currentInfo.userName,
        userid: currentInfo.userId,
        code: phoneCode
      })
      console.log(data, "调用前校验返回的参数")
      const { bindFlag, selfFlag, userid, phoneFlag } = data

      if (!selfFlag && phoneFlag) {
        wx.hideLoading()
        // 手机号已注册联系管理员
        Dialog.confirm({
          title: '提示',
          message: '该手机号已被注册，请更换手机号或联系客服处理',
          context: this,
          selector: '#my-dialog',
          messageAlign: "left"
        })
        return
      } else if (phoneFlag && selfFlag) {
        wx.hideLoading()
        // 手机号已绑定
        this.setData({
          pastUserId: userid,
          confirmValue: "",
          show: true
        })
        return
      }
      if (type !== "1") {
        // 正常绑定
        await phoneBindingApi({
          userid: currentInfo.userId,
          name: currentInfo.userName,
          phoneNo: mobile
        })
      } else {
        let boundUserid = ""
        if (!phoneFlag) {
          boundUserid = currentInfo.userId;
        } else {
          boundUserid = userid
        }
        // 换绑
        await phoneChangeBindingApi({
          userid: currentInfo.userId,
          boundUserid: boundUserid,
          name: currentInfo.userName,
          phoneNo: mobile
        })
      }
      wx.hideLoading()
      wx.setStorageSync('currentInfo', { ...currentInfo, phoneNo: mobile })
      wx.redirectTo({
        url: `/pages/openSuccess/index?type=${type}`
      })
    } catch (error) {
      wx.hideLoading()
      console.log(error, "error")
      if (error.code === 999) {
        wx.showToast({
          title: error.msg,
          icon: 'none',
          // image: '/images/failed.png',
          duration: 2000
        })
        return
      }
      wx.redirectTo({
        url: `/pages/openFailed/index?msg=${error.msg}&type=${type}`
      })
    }

    // wx.redirectTo()
    // 跳转成功/失败页面
  },
  onValueChange({ detail, currentTarget }) {
    const { key } = currentTarget.dataset
    this.setData({
      [key]: detail
    })
  },
  timerFn() {
    this.setData({
      countdown: 59
    })
    const timer = setInterval(() => {
      const { countdown: _countdown } = this.data
      this.setData({
        countdown: _countdown - 1
      })
      if (_countdown - 1 === 0) {
        clearInterval(timer)
      }
    }, 1000)
  },
  onClose() {
    this.setData({
      show: false
    })
  },
  async onConfirm() {
    const { confirmValue, pastUserId } = this.data
    if (confirmValue !== "我已知晓") {
      wx.showToast({
        title: "请输入“我已知晓”后进行确定操作",
        icon: 'none',
        // image: '/images/failed.png',
        duration: 2000
      })
      return
    }
    // 注销绑定
    try {
      const { currentInfo, mobile } = this.data
      await deregisterBindingApi({
        userid: currentInfo.userId,
        boundUserid: pastUserId,
        name: currentInfo.userName,
        phoneNo: mobile
      })
      wx.setStorageSync('currentInfo', { ...currentInfo, phoneNo: mobile })
      wx.redirectTo({
        url: "/pages/openSuccess/index"
      })
    } catch (error) {
      console.log(error, "error")
      wx.redirectTo({
        url: "/pages/openFailed/index?msg=" + error.msg
      })
    }

  },

  async getPhoneNumber(e) {
    console.log("登录e", e); //e的值如下图所示
    if (e && e.detail && e.detail.code) {
      console.log(e)
      const { data } = await getUserPhoneNumberApi(e.detail.code)
      this.setData({
        mobile: data,
        phoneCode: e.detail.code
      })
    }
  }
})