/* pages/openWeChat/index.wxss */
page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #F5F5F7;
}

.openWeChat {
    position: relative;
    width: 690rpx;
    margin: 0 auto;

    .openWeChat-card {
        margin-bottom: 24rpx;
        padding: 40rpx 40rpx;
        border-radius: 16rpx;
        position: relative;
        height: min-content;
        overflow: hidden;
        margin-top: 28rpx;

        .content {
            position: relative;
            color: #ffffff;

            .header {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .title {
                    font-weight: 600;
                    font-size: 36rpx;
                    font-family: PingFangSC, PingFang SC;
                }

                .up {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #FFFFFF;
                    line-height: 32rpx;
                    text-align: left;
                    font-style: normal;
                }

                .currentIdentity {
                    width: 128rpx;
                    height: 48rpx;
                    background: #FFFFFF;
                    border-radius: 24rpx;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 24rpx;
                    color: #1677FF;
                    line-height: 32rpx;
                    text-align: left;
                    font-style: normal;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            .main {
                font-size: 24rpx;
                padding: 16rpx 0;

                >view {
                    &:not(:nth-of-type(1)) {
                        margin-top: 16rpx;
                    }
                }

                .tag {
                    display: flex;
                    align-items: center;

                    .tags {
                        background: #FFFFFF;
                        border-radius: 6rpx;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #0AAF35;
                        line-height: 30rpx;
                        text-align: left;
                        font-style: normal;
                        display: flex;
                        align-items: center;
                        padding: 4rpx 8rpx;

                        .drop {
                            width: 8rpx;
                            height: 8rpx;
                            background: #0AAF35;
                            border-radius: 50%;
                            margin-right: 8rpx;
                        }
                    }

                    .tags-no-open {
                        background: #FFFFFF;
                        border-radius: 6rpx;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 22rpx;
                        color: #ABABAB;
                        line-height: 30rpx;
                        text-align: left;
                        font-style: normal;
                        display: flex;
                        align-items: center;
                        padding: 4rpx 8rpx;

                        .drop {
                            width: 8rpx;
                            height: 8rpx;
                            background: #ABABAB;
                            border-radius: 50%;
                            margin-right: 8rpx;
                        }
                    }
                }
            }
        }
    }

    .form {
        .title {
            font-weight: 600;
            font-family: PingFangSC, PingFang SC;
            font-size: 32rpx;
            color: #1D2023;
            line-height: 44rpx;
            text-align: left;
            font-style: normal;
            padding: 32rpx 0 0 30rpx;
            margin-bottom: 20rpx;
        }

        .yzm {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #1677FF;
            line-height: 36rpx;
            text-align: center;
            font-style: normal;
            height: 36rpx;
        }

        height: 228rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        margin: 0 auto;
        overflow: hidden;
    }

    .hint {
        background: #FFFBE6;
        border-radius: 16rpx;
        border: 2rpx solid #FFF1B8;
        padding: 28rpx 24rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #303133;
        line-height: 38rpx;
        text-align: left;
        font-style: normal;
        display: flex;
        margin-top: 24rpx;
        margin-bottom: 32rpx;

        .text {
            margin-left: 14rpx;
            margin-top: 5rpx;
        }
    }

    .btn {
        border-radius: 16rpx;
        overflow: hidden;
    }
}

.dialog-confirm {
    padding: 0 40rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #747677;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;

    .red {
        color: #F56C6C;
    }

    .confirm-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #000000;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        margin-top: 26rpx;
    }

    .field-index--van-field {
        padding-left: 0;
    }
}