import { getUserIdKeyApi, getUserIdentifyInfoApi } from "@/api/index"

Page({
    data: {
        name: "",
        idCard: ""
    },
    onNameChange(event: { detail: any; }) {
        // event.detail 为当前输入的值
        console.log(event.detail);
        this.setData({
            name: event.detail,
        });
    },
    onIdCardChange(event: { detail: any; }) {
        // event.detail 为当前输入的值
        console.log(event.detail);
        this.setData({
            idCard: event.detail,
        });
    },
    async onSubmit() {
        const { name, idCard } = this.data
        if (!name) {
            wx.showToast({
                title: "请输入姓名",
                icon: 'none',
                // image: '/images/failed.png',
                duration: 2000
            })
            return
        }
        if (!idCard) {
            wx.showToast({
                title: "请输入身份证号",
                icon: 'none',
                // image: '/images/failed.png',
                duration: 2000
            })
            return
        }
        if (!/^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}([0-9Xx])$/.test(idCard)) {
            wx.showToast({
                title: "请填写正确的身份证号",
                icon: 'none',
                // image: '/images/failed.png',
                duration: 2000
            })
            return
        }
        wx.showLoading({
            title: '请稍后...',
            mask: true
        })
        try {
            const { name, idCard } = this.data
            const { data } = await getUserIdKeyApi({
                idCardNumber: idCard,
                name: name
            })
            console.log(data, "userIdKey")
            wx.startFacialRecognitionVerify({
                // name: name,
                // idCardNumber: idCard,
                userIdKey: data,
                async success(e) {
                    wx.showLoading({
                        title: '正在核对结果...',
                        mask: true
                    })
                    const { verifyResult, errCode } = e
                    console.log("success", e)
                    if (errCode === 0) {
                        const { data } = await getUserIdentifyInfoApi({
                            verifyResult: verifyResult
                        })
                        console.log(data, "人脸结果")
                        wx.hideLoading()
                        wx.reLaunch({
                            url: "/pages/home/<USER>"
                        })
                    }
                },
                fail(e) {
                    console.log("fail", e)
                },
                complete(e) {
                    console.log("complete", e)
                }
            })
        } catch (error) {
            console.error("提交实名错误：" + error)
        } finally {
            wx.hideLoading()
        }

        console.log("提交实名", this.data)
    }
})
