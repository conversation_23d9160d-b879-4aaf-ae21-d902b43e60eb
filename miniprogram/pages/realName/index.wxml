<!--index.wxml-->
<navigation-bar title="实名资料提交" color="black" background="#FFF"></navigation-bar>
<view style="height: 100vh;position: relative;">
  <view style="position: absolute;top: 0;right: 0;left: 0;bottom: 0;">
    <images-url src="/images/bj.png" width="100vw" height="714rpx"></images-url>
  </view>
  <!-- <van-divider contentPosition="center">用户绑定平台</van-divider> -->
  <view class="realName">
    <view class="logo">
      <images-url src="/images/logo.png" width="400rpx" height="278rpx"></images-url>
    </view>

    <view class="card">
      <view>
        <van-field value="{{ name }}" name="name" title-width="60px" label="姓名" placeholder="请输入姓名" bind:change="onNameChange" />
        <van-field value="{{ idCard }}" name="idCard" title-width="60px" label="身份证号" placeholder="请输入身份证号" bind:change="onIdCardChange" />
      </view>
      <view class="btn">
        <van-button type="info" block bind:tap="onSubmit">下一步</van-button>
      </view>
      <view style="display: flex;justify-content: flex-end;margin-top: 15rpx;">
        <customer-service />
      </view>
    </view>
  </view>
</view>