/* pages/realName/index.wxss */
page {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.realName {
    width: 690rpx;
    margin: 0 auto;
    padding-bottom: 60rpx;

    .logo {
        width: min-content;
        margin: 0 auto;
        margin-top: 55rpx;
    }

    .card {
        margin-top: 64rpx;
        position: relative;
        padding: 15px 20px;
        height: 636rpx;
        display: flex;
        flex-direction: column;
        border-radius: 16rpx;
        background: #FFFFFF;
        margin-bottom: 26rpx;
    }

    .btn {
        border-radius: 16rpx;
        overflow: hidden;
        margin-top: 56rpx;
    }
}