import { listIdentitiesApi, loginApi } from "@/api/index"


Page({
    data: {
        checked: true,
    },
    onChange({ detail }) {
        // 需要手动对 checked 状态进行更新
        this.setData({ checked: detail });
    },
    async goTo() {
        console.log("跳转")
        const { data } = await listIdentitiesApi()
        if (!data || data.length === 0) {
            wx.reLaunch({
                url: "/pages/selectionMethod/index"
            })
        } else {
            wx.reLaunch({
                url: "/pages/home/<USER>"
            })
        }
    },
    login() {
        console.log("登录")
        const that = this
        function _login() {
            wx.showLoading({
                title: '登录中...',
                mask: true
            })
            wx.login({
                success: async (res: { code: any; errMsg: string }) => {
                    if (res.code) {
                        //发起网络请求换取token
                        const { data } = await loginApi(
                            { code: res.code, appId: "wx1390f03c83bdebbf" }
                        )
                        console.log(data, "登录接口返回值")
                        wx.setStorageSync('tokens', data.token)
                        wx.hideLoading()
                        that.goTo()
                    } else {
                        console.log('登录失败！' + res.errMsg)
                    }
                }
            })
        }
        wx.getStorage({
            key: 'tokens',
            success: (res: { data: string | undefined }) => {
                if (res.data) {
                    that.goTo()
                } else {
                    _login()
                }
            },
            fail: (error: string) => {
                console.error(error)
                _login()
            }
        });
    }
})
