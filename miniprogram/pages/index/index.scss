/**index.scss**/
page {
    // height: 100vh;
    display: flex;
    flex-direction: column;
}

.login {
    width: 690rpx;
    margin: 0 auto;
    padding-bottom: 60rpx;

    .logo {
        width: min-content;
        margin: 0 auto;
        margin-top: 55rpx;
    }

    // background: url("../../images/bj.png");
    .bd {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 32rpx;

        .bd-text {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            color: #1D2023;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
            margin: 0 10rpx;
        }

        .left {
            width: 132rpx;
            height: 2rpx;
            background: linear-gradient(90deg, rgba(62, 67, 73, 0) 0%, #1D2023 100%);
        }

        .right {
            width: 132rpx;
            height: 2rpx;
            background: linear-gradient(90deg, #1D2023 0%, rgba(62, 67, 73, 0) 100%);
        }
    }

    .card {
        margin-top: 64rpx;
        position: relative;
        padding: 15px 20px;
        height: 636rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        border-radius: 16rpx;
        background: #FFFFFF;
        margin-bottom: 26rpx;
        padding-top: 76rpx;


        .text {
            text-indent: 2em;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #1D2023;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
        }
    }

    .flow-list {
        margin-top: 50rpx;

        .flow {
            display: flex;
            align-items: center;
        }

        .flow-text {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 28rpx;
            color: #1D2023;
            line-height: 40rpx;
            text-align: left;
            font-style: normal;
            margin-left: 18rpx;
        }
    }

    .btn {
        border-radius: 16rpx;
        overflow: hidden;
    }
}