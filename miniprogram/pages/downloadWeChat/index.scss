/* pages/openSuccess/index.wxss */
page {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.downloadWeChat {
    position: relative;
    width: 100%;
    height: 100%;

    .title {}

    .content {
        position: relative;
        margin: 0 auto;
        width: 662rpx;

        .title {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 32rpx;
            color: #FFFFFF;
            line-height: 44rpx;
            text-align: center;
            font-style: normal;
            margin-top: 32rpx;
            margin-bottom: 32rpx;
        }

        .main {
            width: 662rpx;
            height: 1168rpx;
            background: #FFFFFF;
            border-radius: 16rpx;


            .logo {
                width: 100%;
            }

            .download {
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .bd {
                display: flex;
                justify-content: center;
                align-items: center;
                margin-top: 15rpx;

                .bd-text {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 28rpx;
                    color: #5C6371;
                    line-height: 40rpx;
                    text-align: left;
                    font-style: normal;
                    margin: 0 10rpx;
                }

                .left {
                    width: 132rpx;
                    height: 2rpx;
                    background: linear-gradient(90deg, rgba(62, 67, 73, 0) 0%, #1D2023 100%);
                }

                .right {
                    width: 132rpx;
                    height: 2rpx;
                    background: linear-gradient(90deg, #1D2023 0%, rgba(62, 67, 73, 0) 100%);
                }
            }

            .qrCode {
                position: relative;
                width: 344rpx;
                height: 344rpx;
                display: flex;
                justify-content: center;
                align-items: center;

                .kuang {
                    position: absolute;
                    top: 0;
                    right: 0;
                    left: 0;
                    bottom: 0;
                }

                .ma {
                    width: 90%;
                    height: 90%;
                    z-index: 999;
                    margin: 0 auto;
                }


            }
        }
    }
}