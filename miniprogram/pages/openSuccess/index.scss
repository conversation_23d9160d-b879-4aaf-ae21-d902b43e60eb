/* pages/openSuccess/index.wxss */
page {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.openSuccess {
    flex: 1;
    display: flex;
    flex-direction: column;
    // justify-content: center;
    align-items: center;
    padding-top: 208rpx;

    .main-content {
        width: 480rpx;
        height: 188rpx;
        background: #F5F5F5;
        border-radius: 16rpx;
        margin-top: 32rpx;
        padding: 24rpx 32rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #1D2023;
        line-height: 45rpx;
        text-align: left;
        font-style: normal;
    }

    .btn {
        margin-top: 44rpx;
        display: flex;
        align-items: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28rpx;
        color: #1D2023;
        line-height: 40rpx;
        text-align: right;
        font-style: normal;

        .hint {
            font-weight: 500;
            margin-left: 8rpx;
            font-size: 28rpx;
            color: #1677FF;
        }
    }
}

.change-binding {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #1677FF;
    line-height: 38rpx;
    text-align: center;
    font-style: normal;
    margin-bottom: 90rpx;
}