// pages/changeKnows/index.ts
Page({

    /**
     * 页面的初始数据
     */
    data: {
        checked: false
    },
    goTo(e: any) {
        const { checked } = this.data
        if (!checked) {
            wx.showToast({
                title: "请先同意隐私协议",
                icon: 'none',
                // image: '/images/failed.png',
                duration: 2000
            })
            return
        }
        const { url } = e.currentTarget.dataset
        wx.redirectTo({
            url
        })
    },
    onChange(event) {
        this.setData({
            checked: event.detail,
        });
    },
})