/* pages/changeKnows/index.wxss */

page {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #F7F8FA;
}

.changePhone {
    .card {
        width: 658rpx;
        height: 662rpx;
        background: #FFFFFF;
        border-radius: 16rpx;
        margin: 0 auto;
        margin-top: 254rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 32rpx 44rpx;

        .title {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 36rpx;
            color: #1D2023;
            line-height: 50rpx;
            font-style: normal;
        }

        .text {
            margin-top: 30rpx;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 28rpx;
            color: #1D2023;
            line-height: 44rpx;
            text-align: left;
            font-style: normal;
        }

        .btn {
            margin-top: 98rpx;
            width: 100%;

            .xieyi {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #5C6371;
                line-height: 36rpx;
                text-align: left;
                font-style: normal;

                span {
                    color: #1677FF;
                }
            }
        }
    }
}