<!--pages/switchIdentities/index.wxml-->
<navigation-bar title="身份" color="black" background="#FFF"></navigation-bar>
<view class="switchIdentities-overflow">
    <view>
        <view class="switchIdentities">
            <identity-card wx:for="{{list}}" wx:key="{{index}}" is-expand info="{{item}}" current-identity="{{item.isDefault}}" bind:tap="changeIdentity" data-userinfobindid="{{item.userInfoBindId}}" />
        </view>
    </view>
</view>
<view class="switchIdentities-btn">
    <view class="btn">
        <van-button loading="{{loading}}" loading-text="更新中..." icon="replay" block type="info" bind:tap="updateIdentity">更新身份</van-button>
    </view>
</view>