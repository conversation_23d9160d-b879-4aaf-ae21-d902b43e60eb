import { listIdentitiesApi, updateDefaultIdentityApi, updateIdentitiesApi } from "@/api/index"

// pages/switchIdentities/index.ts
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    loading: false
  },
  onLoad() {
    this.getIdentity()
  },
  async updateIdentity() {
    await updateIdentitiesApi()
    this.getIdentity()
  },
  async getIdentity() {
    // 1. 创建映射表
    const userTypeOrder = {
      '1': 0,
      '3': 1,
      '2': 2,
      '0': 3
    };

    // 2. 定义比较函数
    function compare(a, b) {
      return userTypeOrder[a.userType] - userTypeOrder[b.userType];
    }
    try {
      this.setData({
        loading: true
      })
      const { data } = await listIdentitiesApi()
      console.log(data, "dataa")
      const _list = data.filter(item => item.userId).sort(compare)
      _list.push(...data.filter(item => !item.userId))
      this.setData({
        list: _list,
      })
    } catch (error) {
      console.error("获取身份失败：" + error)
    } finally {
      const timer = setTimeout(() => {
        this.setData({
          loading: false
        })
        clearTimeout(timer)
      }, 500)
    }
  },
  async changeIdentity(e) {
    const { userinfobindid } = e.currentTarget.dataset
    console.log(userinfobindid, e, "userInfoBindId")
    await updateDefaultIdentityApi(userinfobindid)
    this.updateIdentity()
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})