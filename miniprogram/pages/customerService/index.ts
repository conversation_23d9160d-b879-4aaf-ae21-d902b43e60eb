import { getHelpUrlApi } from "@/api/index"

// pages/customerService/index.ts
Page({

    /**
     * 页面的初始数据
     */
    data: {
        userType: "0",
        imgUrl: ""
    },
    /**
     * 生命周期函数--监听页面加载
     */
    async onLoad() {
        const userType = (wx.getStorageSync('currentInfo') || {})?.userType || "0"
        const { data: imgUrl } = await getHelpUrlApi(userType)
        this.setData({
            userType, imgUrl
        })
    },
    downLoad() {
        console.log("下载")
        const { imgUrl } = this.data;
        //下载
        // wx.downloadFile({
        //     url: imgUrl,
        //     success: (res) => {
        //         wx.showShareImageMenu({
        //             path: res.tempFilePath //要分享的图片地址，必须为本地路径或临时路径
        //         })
        //     }
        // })
        wx.previewImage({
            current: imgUrl, // 当前显示图片的http链接
            urls: [imgUrl] // 需要预览的图片http链接列表
          })
    }
})