<!--index.wxml-->
<navigation-bar title="实名资料提交" color="black" background="#FFF"></navigation-bar>
<view class="infoSubmit" style="z-index: 10;">
  <view class="hint">
    <images-url src="/images/hint.png" width="48rpx" height="48rpx"></images-url>
    <view class="text" wx:if="{{type === '1'}}">
      请确保是本人操作并填写您本人信息进行认证。港澳台默认是港澳台身份证或护照（如果证件中有括号，请使用英文括号()，有字母统一大写），无效的再尝试使用港澳台通行证。
    </view>
    <view class="text" wx:if="{{type !== '1'}}">
      请确保是本人操作并填写您本人信息进行认证。留学生、外籍人员默认是护照（如果证件中有括号，请使用英文括号()，有字母统一大写）。
    </view>
  </view>
  <view class="form">
    <van-field bind:focus="onFocus" bind:blur="onBlur" name="name" value="{{ form.name }}" data-key="name" label="姓名" placeholder="请输入姓名" bind:change="onValueChange" />
    <view style="position: relative;">
      <van-field hold-keyboard="{{false}}" name="idCardTypeText" value="{{ idCardTypeText }}" placeholder="请选择证件类型" disabled is-link>
        <view slot="input">
          <view wx:if="{{!idCardTypeText}}" style="color: #C8C9CC;">请选择证件类型</view>
          <view wx:else>{{ idCardTypeText}}</view>
        </view>
        <view slot="label" style="color: var(--field-label-color,#646566)">证件类型</view>
      </van-field>
      <view bind:tap="showPopup" style="position: absolute;z-index: 1000;top: 0;left: 0;right: 0;bottom:0;"></view>
    </view>
    <van-field bind:focus="onFocus" bind:blur="onBlur" bind:focus="onFocus" name="idCard" value="{{ form.idCard }}" data-key="idCard" label="证件号码" placeholder="请输入证件号码" bind:change="onValueChange" />
    <van-field bind:focus="onFocus" bind:blur="onBlur" name="userid" value="{{ form.userid }}" data-key="userid" label="学工号" placeholder="请输入学号或者工号" bind:change="onValueChange" />
    <view style='font-size: 28rpx;margin-left: 30rpx;margin-top: 20rpx;'>
      <view class='van-field__label field-index--van-field__label' style="margin-bottom: 20rpx;">请上传佐证材料</view>
      <van-uploader file-list="{{ form.imgList }}" deletable="{{ true }}" max-count="3" multiple bind:after-read="afterRead" bind:delete="imgDelete" />
    </view>
  </view>
  <view class="operate">
    <view class="agreement">
      <!-- <van-checkbox value="{{ checked }}" bind:change="onCheckChange" icon-size="32rpx">
                <text class="text">
                    信息仅用于身份验证，平台保障您的信息安全
                </text>
            </van-checkbox> -->
      <view class="text">
        <van-icon name="shield-o" size="30rpx" style="margin-right: 8rpx;margin-top: 4rpx;" />
        信息仅用于身份验证，平台保障您的信息安全
      </view>
      <customer-service id="target" />
    </view>
    <view style="overflow: hidden;border-radius: 16rpx;">
      <van-button block type="info" bind:tap="onSubmit" disabled="{{!checked}}">提交</van-button>
    </view>
  </view>
  <van-toast id="infoSubmit" />
</view>
<van-popup z-index="1000" position="bottom" show="{{ show }}" bind:close="onClose">
  <van-picker columns="{{ columns }}" data-key="idCardType" show-toolbar bind:cancel="onClose" bind:confirm="onValueChange" />
</van-popup>