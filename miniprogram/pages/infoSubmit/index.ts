// import Toast from "@/miniprogram_npm/@vant/weapp/toast/toast";
import { verificationOfDocumentApi, verificationOfDocumentWjApi, uploadBucket } from "@/api/index"
// pages/infoSubmit/index.ts

Page({
  /**
   * 页面的初始数据
   * 
   */
  data: {
    errorMessage: "",
    show: false,
    columns: [],
    checked: true,
    idCardTypeText: "",
    type: "1",// 1：港澳台 2：留学生
    form: {
      name: "",
      idCardType: "",
      idCard: "",
      userid: "",
      imgList: []
    },
    focus: false
  },
  onLoad(options) {
    console.log(options, "options")
    const { type } = options
    if (type === "1") {
      this.setData({
        columns: [
          { text: "身份证", value: "0" },
          { text: "护照", value: "1" },
          { text: "港澳居民身份证", value: "3" },
          { text: "港澳来往内地通行证", value: "4" },
          { text: "港澳居民居住证", value: "5" },
          { text: "台湾居住的有效身份证明", value: "6" },
          { text: "台湾居民来往大陆通行证", value: "7" },
          { text: "台湾居民居住证", value: "8" },
          { text: "暂住证", value: "9" }
        ] as any
      })
    } else {
      this.setData({
        columns: [
          { text: "护照", value: "1" }
        ] as any
      })
    }
    this.setData({
      type
    })
  },
  onValueChange({ detail, currentTarget }) {
    const { key } = currentTarget.dataset
    if (key === "idCardType") {
      // 身份证类型
      const { value, text } = detail.value
      this.onClose()
      this.setData({
        [`form.${key}`]: value
      })
      this.setData({
        idCardTypeText: text
      })
    } else {
      this.setData({
        [`form.${key}`]: detail
      })
    }
    console.log(key, detail)
  },
  onChange({ detail }) {
    this.setData({
      errorMessage: detail
    })
  },
  onCheckChange(event) {
    this.setData({
      checked: event.detail,
    });
  },
  onFocus() {
    this.setData({ focus: true })
  },
  onBlur() {
    this.setData({ focus: false })
  },
  showPopup() {
    let index = 0
    const timer = setInterval(() => {
      const { focus } = this.data
      if (!focus) {
        this.setData({ show: true });
        clearInterval(timer)
      }
      index++
      if (index === 3) {
        clearInterval(timer)
      }
    }, 400)
  },
  onClose() {
    this.setData({ show: false });
  },
  async afterRead(event) {
    const { file } = event.detail;
    console.log(event.detail, event.detail)
    file.forEach(item => {
      uploadBucket(item.url).then(({ data }) => {
        const { form } = this.data;
        this.setData({
          ['form.imgList']: [
            ...form.imgList,
            {
              url: data.url,
              isImage: true,
              deletable: true
            }]
        })
      })
    })
    // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
  },
  async onSubmit() {
    const { checked, form, type } = this.data
    const hintList = [
      { value: !!form.name, hint: "请输入姓名" },
      { value: !!form.idCardType, hint: "请选择证件类型", },
      { value: !!form.idCard, hint: "请输入证件号码", },
      { value: !!form.userid, hint: "请输入学工号", },
      { value: form.imgList.length !== 0, hint: "请上传佐证材料", }]
    const index = hintList.findIndex((item) => {
      if (!item.value) {
        wx.showToast({
          title: item.hint,
          icon: 'none',
          // image: '/images/failed.png',
          duration: 2000
        })
      }
      return !item.value
    })
    if (index !== -1) {
      // 必填校验
      return
    }
    console.log(checked, "checked")
    if (!checked) {
      wx.showToast({
        title: '请先同意用户协议 ',
        icon: 'none',
        duration: 2000
      })
      return
    };
    console.log("提交", form)
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      })
      if (type === '1') {
        // 港澳台
        console.log(verificationOfDocumentApi)
        await verificationOfDocumentApi({
          certificateImg: form.imgList.map(item => item.url).join(","),
          identityNumber: form.idCard,
          identityType: form.idCardType,
          name: form.name,
          userid: form.userid
        })
      } else {
        // 留学生
        console.log(verificationOfDocumentWjApi)
        await verificationOfDocumentWjApi({
          certificateImg: form.imgList.map(item => item.url).join(","),
          identityNumber: form.idCard,
          identityType: form.idCardType,
          name: form.name,
          userid: form.userid
        })
      }
      wx.redirectTo({
        url: "/pages/home/<USER>"
      })
    } catch (error) {
      console.error("提交资料失败：" + error)
      wx.redirectTo({
        url: "/pages/submitFailed/index?msg=" + error.msg
      })
    } finally {
      wx.hideLoading()
    }
  },
  imgDelete({ detail }) {
    console.log(detail, "detaildetaildetail")
    const { form } = this.data;
    form.imgList.splice(detail.index, 1)
    this.setData({
      ['form.imgList']: form.imgList
    })
  }
})