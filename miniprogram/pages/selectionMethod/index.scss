/* pages/selectionMethod/index.wxss */

page {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.selectionMethod {
    width: 690rpx;
    margin: 0 auto;
    position: relative;
    padding-bottom: 60rpx;

    .card1 {
        margin-top: 32rpx;
        padding: 40rpx 32px;
        height: 176rpx;
        background-color: #ffffff;
        border-radius: 16rpx;
        display: flex;
        align-items: center;

        .avatorName {
            width: 96rpx;
            height: 96rpx;
            border-radius: 50%;
            background-color: #3A93F8;
            color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 28rpx;
        }

        .title {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 32rpx;
            color: #1D2023;
            line-height: 44rpx;
            text-align: left;
            font-style: normal;
            display: flex;
            align-items: center;

            .tag {
                width: 104rpx;
                height: 40rpx;
                background: #30BD90;
                border-radius: 8rpx;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #FFFFFF;
                line-height: 32rpx;
                text-align: left;
                font-style: normal;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 16rpx;
            }

            .tags {
                width: 104rpx;
                height: 40rpx;
                background: #B7C2D2;
                border-radius: 8rpx;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 24rpx;
                color: #FFFFFF;
                line-height: 32rpx;
                text-align: left;
                font-style: normal;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-left: 16rpx;
            }
        }

        .xuexiao {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #1D2023;
            line-height: 34rpx;
            text-align: left;
            font-style: normal;
        }
    }

    .card2 {
        margin-top: 32rpx;
        padding: 40rpx 40rpx;
        height: 916rpx;
        background-color: #ffffff;
        border-radius: 16rpx;
        display: flex;
        flex-direction: column;

        .title {
            font-weight: 600;
            font-size: 32rpx;
            color: #1D2023;
            line-height: 44rpx;
            text-align: left;
            font-style: normal;
        }

        .text {
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 24rpx;
            color: #5C6371;
            line-height: 34rpx;
            text-align: left;
            font-style: normal;
            margin-top: 16rpx;
        }

        .flow-list {
            margin-top: 38rpx;

            .flow {
                width: 626rpx;
                height: 168rpx;
                background: #F1F3F7;
                border-radius: 16rpx;
                padding: 40rpx 32rpx;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .left {
                    display: flex;
                }

                .flow-title {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 32rpx;
                    color: #1D2023;
                    line-height: 44rpx;
                    text-align: left;
                    font-style: normal;
                }

                .flow-text {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 24rpx;
                    color: #5C6371;
                    line-height: 32rpx;
                    text-align: left;
                    font-style: normal;
                }
            }
        }
    }
}