// pages/selectionMethod/index.ts
Page({
    data: {
        card: {},
        avatorName: "",
    },
    onLoad() {
        this.setData({
            card: wx.getStorageSync('currentInfo') || {},
            avatorName: this.processString((wx.getStorageSync('currentInfo') || {})?.userName || "--")
        })
    },
    goTo(e: any) {
        const { url } = e.currentTarget.dataset
        wx.navigateTo({
            url
        })
    },
    processString(str) {
        console.log(str, "stt")
        const isAllChinese = /^[\u4e00-\u9fff]+$/.test(str);
        const isAllEnglish = /^[a-zA-Z]+$/.test(str);
        if (isAllChinese) {
            return str.slice(-2);
        } else if (isAllEnglish) {
            return str.slice(0, 2);
        } else {
            return str.slice(-2);
        }
    },
})