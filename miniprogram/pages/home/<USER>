import { listIdentitiesApi } from "@/api/index"


Page({
  data: {
    card: {
      name: "xxx", // 名字
      realName: false, // 是否实名
      openWechat: false // 是否开通企业微信
    },
    avatorName: "",
    cardInfo: {},
    cardList: [],
    render: false
  },
  onShow() {
    console.log("onshow")
    this.onLoad()
  },
  async onLoad() {
    const tokens = wx.getStorageSync('tokens')
    if (!tokens) {
      wx.reLaunch({
        url: "/pages/index/index"
      })
      return
    }
    const { data } = await listIdentitiesApi()
    if (!data || data.length === 0) {
      wx.reLaunch({
        url: "/pages/index/index"
      })
      return
    }
    this.setData({
      render: true
    })
    console.log(data, "Dataaa")
    const currentInfo: any = data.find(item => item.isDefault) || {}
    this.setData({
      card: {
        name: currentInfo?.userName || "--",
        realName: currentInfo?.authStatus,
        openWechat: currentInfo?.openStatus
      },
      cardInfo: currentInfo,
      cardList: data,
      avatorName: this.processString(currentInfo?.userName || "--")
    })
  },
  goTo(e: any) {
    const { url } = e.currentTarget.dataset
    const { cardInfo } = this.data
    wx.setStorageSync('currentInfo', cardInfo)
    if (url.includes("/pages/openWeChat/index")) {
      const { card } = this.data
      if (card.openWechat) {
        // 跳转已绑定页面
        wx.navigateTo({
          url: "/pages/openSuccess/index"
        })
        return
      }
    }
    wx.navigateTo({
      url
    })
  },
  processString(str) {
    console.log(str, "stt")
    const isAllChinese = /^[\u4e00-\u9fff]+$/.test(str);
    const isAllEnglish = /^[a-zA-Z]+$/.test(str);
    if (isAllChinese) {
      return str.slice(-2);
    } else if (isAllEnglish) {
      return str.slice(0, 2);
    } else {
      return str.slice(-2);
    }
  },
})