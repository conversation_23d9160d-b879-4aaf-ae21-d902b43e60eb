/**index.scss**/
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #F7F8FA;
}

.home {
  width: 690rpx;
  margin: 0 auto;
  padding-bottom: 60rpx;
  position: relative;

  .card1 {
    margin-top: 32rpx;
    padding: 40rpx 32px;
    height: 176rpx;
    background-color: #ffffff;
    border-radius: 16rpx;
    display: flex;
    align-items: center;

    .avatorName {
      width: 96rpx;
      height: 96rpx;
      border-radius: 50%;
      background-color: #3A93F8;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28rpx;
    }

    .title {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      color: #1D2023;
      line-height: 44rpx;
      text-align: left;
      font-style: normal;
      display: flex;
      align-items: center;

      .tag {
        width: 104rpx;
        height: 40rpx;
        background: #30BD90;
        border-radius: 8rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
        line-height: 32rpx;
        text-align: left;
        font-style: normal;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 16rpx;
      }

      .tags {
        width: 104rpx;
        height: 40rpx;
        background: #B7C2D2;
        border-radius: 8rpx;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 24rpx;
        color: #FFFFFF;
        line-height: 32rpx;
        text-align: left;
        font-style: normal;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 16rpx;
      }
    }

    .xuexiao {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #1D2023;
      line-height: 34rpx;
      text-align: left;
      font-style: normal;
    }
  }

  .operate {
    display: flex;
    justify-content: space-between;
    margin: 24rpx 0;

    .left {
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 32rpx;
      color: #1D2023;
      line-height: 44rpx;
      text-align: left;
      font-style: normal;
    }

    .right {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #1D2023;
      line-height: 34rpx;
      text-align: left;
      font-style: normal;
      display: flex;
      align-items: center;
    }
  }

  .card3 {
    padding: 15px 20px;
    // height: 515rpx;
    border-radius: 16rpx;
    background: #FFFFFF;

    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .left {
        font-family: PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 32rpx;
        color: #1D2023;
        line-height: 44rpx;
        text-align: left;
        font-style: normal;
      }



      .right {
        color: #0079DE;
        font-family: PingFangSC,
          PingFang SC;
        font-weight: 600;
        font-size: 28rpx;
        text-align: left;
        font-style: normal;
        display: flex;
        align-items: center;
      }
    }

    .content {
      display: flex;
      justify-content: flex-start;
      // height: 388rpx;
      margin-top: 26rpx;
      flex-wrap: wrap;
      position: relative;

      .border {
        height: 1rpx;
        background-color: #EFEFF1;
        width: 100%;
        top: 50%;
        position: absolute;
      }

      .w-border {
        position: absolute;
        width: 100%;
        height: 100%;
        display: flex;

        >view {
          width: 1rpx;
          height: 100%;
          background-color: #EFEFF1;
          margin-left: calc(100% / 3);
        }
      }

      .item {
        width: calc(100% / 3);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26rpx;
        color: #5C6371;
        line-height: 34rpx;
        text-align: right;
        font-style: normal;
        // border-right: 1px solid #EFEFF1;
        width: 33%;
        height: 194rpx;

        // border-right: 1px solid #EFEFF1;
        // border-bottom: 1px solid #EFEFF1;
        &:not(:nth-child(3n)) {
          // background-color: red;
          margin-left: 2rpx;
        }
      }

      .no-border {
        border: none !important;
      }
    }
  }
}