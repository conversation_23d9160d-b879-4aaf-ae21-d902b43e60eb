<!--index.wxml-->
<page-meta>
  <navigation-bar wx:if="{{render}}" title="中南财经政法大学" back="{{false}}" color="black" background="#FFF"></navigation-bar>
</page-meta>

<view wx:if="{{render}}" style="height: 100vh;position: relative;">
  <view style="position: absolute;top: 0;right: 0;left: 0;bottom: 0;">
    <images-url src="/images/bj2.png" width="100vw" height="714rpx"></images-url>
  </view>
  <!-- <van-divider contentPosition="center">用户绑定平台</van-divider> -->
  <view class="home">
    <view class="card1">
      <view class="avatorName" wx:if="{{avatorName}}">
        {{avatorName}}
      </view>
      <images-url wx:else src="/images/avator.png" width="96rpx" height="96rpx"></images-url>
      <view style="height: 85rpx;margin-left: 16rpx;display: flex;flex-direction: column;justify-content: space-between;">
        <view class="title">
          {{card.name}}
          <view class="tag" wx:if="{{card.realName || (cardInfo.userName && !card.userId)}}">已实名</view>
          <view class="tags" wx:else>未实名</view>
        </view>
        <view class="xuexiao">企业微信：{{cardInfo.openStatus?"已开通":"未开通"}}</view>
      </view>
    </view>
    <view class="operate">
      <view class="left">我的身份 ({{cardList.length}}张)</view>
      <view class="right" bind:tap="goTo" data-url="/pages/switchIdentities/index">
        <images-url style="margin-right: 8rpx;" src="/images/home-switch.png" width="32rpx" height="32rpx"></images-url>
        切换身份
      </view>
    </view>
    <identity-card wx:if="{{cardInfo.userName}}" info="{{cardInfo}}" />
    <view class="card3" style="height: {{card.openWechat && cardInfo.userId?'515rpx':'310rpx'}};">
      <view class="title">
        <view class="left">我的服务</view>
        <view class="right">
          <images-url style="margin-right: 8rpx;" src="/images/qw.png" width="38rpx" height="38rpx"></images-url>
          <view>企业微信</view>
        </view>
      </view>
      <view class="content" style="justify-items: flex-start;">
        <view class="border" wx:if="{{card.openWechat && cardInfo.userId}}">
        </view>
        <view class="w-border">
          <view></view>
          <view></view>
        </view>
        <view class="item">
          <images-url style="margin-bottom: 12rpx;" src="/images/home-zxjc.png" width="88rpx" height="88rpx"></images-url>
          <view>在线教程</view>
        </view>
        <view wx:if="{{cardInfo.userId}}" class="item" bind:tap="goTo" data-url="/pages/openWeChat/index?type=2">
          <images-url style="margin-bottom: 12rpx;" src="/images/home-kt.png" width="88rpx" height="88rpx"></images-url>
          <view>企微开通</view>
        </view>
        <view wx:if="{{card.openWechat}}" class="item" bind:tap="goTo" data-url="/pages/changeKnows/index">
          <images-url style="margin-bottom: 12rpx;" src="/images/hb.png" width="88rpx" height="88rpx"></images-url>
          <view>换绑手机号</view>
        </view>
        <view class="item" bind:tap="goTo" data-url="/pages/selectionMethod/index">
          <images-url style="margin-bottom: 12rpx;" src="/images/home-cxrz.png" width="88rpx" height="88rpx"></images-url>
          <view>重新认证</view>
        </view>
      </view>
    </view>
  </view>
</view>