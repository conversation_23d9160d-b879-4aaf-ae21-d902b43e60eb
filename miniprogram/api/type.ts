export namespace IType {
  export interface VerificationForm {
    /**
* 证件照片
*/
    certificateImg?: string;
    /**
     * 证件号码
     */
    identityNumber?: string;
    /**
     * 证件类型
     */
    identityType?: string;
    /**
     * 姓名
     */
    name?: string;
    /**
     * 学工号
     */
    userid?: string;
  }
  export interface SubmitFormBefore {
    /**
* 姓名
*/
    name?: string;
    /**
     * 手机号
     */
    phoneNo?: string;
    /**
     * 学工号
     */
    userid?: string;
    /**
     * 验证码
     */
    verifyCode?: string;
    code?: string
  }
  export interface SubmitForm {
    /**
* 绑定的用户学工号
*/
    boundUserid?: string;
    /**
     * 姓名
     */
    name?: string;
    /**
     * 手机号
     */
    phoneNo: string;
    /**
     * 学工号
     */
    userid?: string;
  }
  /**
* 用户身份信息VO
*
* UserIdentityInfoVO
*/
  export interface UserIdentityInfoVO {
    /**
     * 认证状态
     */
    authStatus: boolean;
    /**
     * 证件图片
     */
    certificateImg?: string;
    /**
     * 部门编码
     */
    deptCode?: string;
    /**
     * 部门名称
     */
    deptName?: string;
    /**
     * 身份证号
     */
    identityNumber?: string;
    /**
     * 是否默认（切换身份后设置默认，下次进入默认显示此身份）
     */
    isDefault: boolean;
    /**
     * 微信openid
     */
    openId?: string;
    /**
     * 开通状态
     */
    openStatus: boolean;
    /**
     * 手机号
     */
    phoneNo?: string;
    /**
     * 工号
     */
    userId?: string;
    /**
     * 用户绑定表主键
     */
    userInfoBindId?: number;
    /**
     * 用户姓名
     */
    userName?: string;
    /**
     * 用户类型(0：其他人员 1：教职工 2：本科生 3：研究生)
     */
    userType?: string;
  }
}