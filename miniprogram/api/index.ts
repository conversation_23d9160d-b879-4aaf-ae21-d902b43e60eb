import request from "@/utils/request"
import { IType } from "./type"

const COMMON_SERVICE = "common-service"

// 登录
export const loginApi = async (data: { appId: string; code: string }) => {
    return await request.get({
        url: `/${COMMON_SERVICE}/auth/login/mp`,
        data
    })
}

export const getUserIdKeyApi = async (data: { idCardNumber: string; name: string }) => {
    return await request.post({
        url: `/${COMMON_SERVICE}/user-info/h5/getUserIdKey`,
        data
    })
}

// 获取用户的认证身份信息列表
export const getUserIdentifyInfoApi = async (data: { verifyResult: string; }) => {
    return await request.get({
        url: `/${COMMON_SERVICE}/user-info/h5/getUserIdentifyInfo`,
        data
    })
}

// 获取身份列表
export const listIdentitiesApi = async () => {
    // return Promise.resolve({
    //     "code": 0, "msg": "成功",
    //     "data": [
    //         {
    //             "userInfoBindId": 32, "openId":
    //                 "owMug5OWiHDtrB4FE80Lds-OmvsU", "userName": "宋睿", "identityNumber": "******************", "userId": "10001", "certificateImg": "https://lskj.lskejisoft.com/wx-platform-dev/app-service/minio/file/20a41b7b-42ad-42e8-b2b3-ce75176f5cc5", "isDefault": true, "authStatus": true, "openStatus": false, "userType": "1", "deptCode": "229", "deptName": "公关部"
    //         },
    //         {
    //             "userInfoBindId": 44, "openId": "owMug5OWiHDtrB4FE80Lds-OmvsU", "userName": "刘安琪", "identityNumber": "427506283314102277", "userId": "10000", "certificateImg": "https://lskj.lskejisoft.com/wx-platform-dev/app-service/minio/file/ff52d1c0-23d1-4993-a68e-a7c1a16c7af9", "isDefault": false, "authStatus": true, "openStatus": false,
    //             "userType": "2", "deptCode": "220", "deptName": "外销部"
    //         },
    //         { 
    //             "userInfoBindId": 44, "openId": "owMug5OWiHDtrB4FE80Lds-OmvsU", "userName": "刘安琪", "identityNumber": "427506283314102277", "certificateImg": "https://lskj.lskejisoft.com/wx-platform-dev/app-service/minio/file/ff52d1c0-23d1-4993-a68e-a7c1a16c7af9", "isDefault": false, "authStatus": true, "openStatus": false,
    //             "userType": "2", "deptCode": "220", "deptName": "外销部"
    //         }]
    // })
    return await request.get<IType.UserIdentityInfoVO[]>({
        url: `/${COMMON_SERVICE}/user/phone/listIdentities`
    })
}

// 更新身份列表
export const updateIdentitiesApi = async () => {
    return await request.post({
        url: `/${COMMON_SERVICE}/user/phone/updateIdentities`
    })
}

// 证件核验-港澳台
export const verificationOfDocumentApi = async (data: IType.VerificationForm) => {
    return await request.post({
        url: `/${COMMON_SERVICE}/user/phone/verificationOfDocument`,
        data
    })
}

// 证件核验-留学生，外籍
export const verificationOfDocumentWjApi = async (data: IType.VerificationForm) => {
    return await request.post({
        url: `/${COMMON_SERVICE}/user/phone/verificationOfDocumentWj`,
        data
    })
}

// 图片上传
export const uploadBucket = async (filePath: string) => {
    return await request.uploadFile({
        url: `/${COMMON_SERVICE}/minio/uploadEncrypt/user-binding`,
        filePath
    });
};

// 更新默认身份
export const updateDefaultIdentityApi = async (userInfoBindId: string) => {
    return await request.post({
        url: `/${COMMON_SERVICE}/user/phone/updateDefaultIdentity?userInfoBindId=${userInfoBindId}`,
        data: { userInfoBindId }
    })
}

// 绑定前校验
export const perBindingVerifyApi = async (params: IType.SubmitFormBefore) => {
    return await request.post<{
        bindFlag?: boolean;
        selfFlag?: boolean;
        userid?: string;
    }>({
        url: `/${COMMON_SERVICE}/user/phone/perBindingVerify`,
        data: params
    })
}

// 绑定
export const phoneBindingApi = async (params: IType.SubmitForm) => {
    return await request.post({
        url: `/${COMMON_SERVICE}/user/phone/binding`,
        data: params
    })
}

// 换绑
export const phoneChangeBindingApi = async (params: IType.SubmitForm) => {
    return await request.post({
        url: `/${COMMON_SERVICE}/user/phone/changeBinding`,
        data: params
    })
}

// 注销绑定
export const deregisterBindingApi = async (params: IType.SubmitForm) => {
    return await request.post({
        url: `/${COMMON_SERVICE}/user/phone/deregisterBinding`,
        data: params
    })
}

// 获取验证码
export const getCodeApi = async (data: { phoneNo: string; }) => {
    return await request.get({
        url: `/${COMMON_SERVICE}/user/phone/get/code`,
        data
    })
}

// 获取客服二维码
export const getHelpUrlApi = async (userType: string) => {
    return await request.get({
        url: `/${COMMON_SERVICE}/user/phone/getHelpUrl`,
        data: { userType }
    })
}

// 获取用户手机号
export const getUserPhoneNumberApi = async (code: string) => {
  return await request.get({
      url: `/${COMMON_SERVICE}/user-info/h5/getUserPhoneNumber`,
      data: { code }
  })
}